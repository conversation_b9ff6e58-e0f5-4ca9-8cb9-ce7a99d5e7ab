<div class="overflow-auto bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Donatur</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Penghubung</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manager</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Creation</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            @forelse ($donasi->getCollection() as $row)
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">IDR {{ number_format($row['amount'], 0, ',', '.') }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row['program'] ?? '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row['donatur'] ?? '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row['penghubung'] ?? '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row['manager'] ?? '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    @if($row['date'] && $row['date'] !== '-')
                        @php
                            $months = [
                                'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                                'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
                            ];
                            $date = \Carbon\Carbon::parse($row['date']);
                            $formattedDate = $date->format('j') . ' ' . $months[$date->format('n') - 1] . ' ' . $date->format('Y H:i:s');
                        @endphp
                        {{ $formattedDate }}
                    @else
                        -
                    @endif
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ ucfirst($row['creation'] ?? '-') }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button @click="$dispatch('view-donasi', { id: '{{ $row['id'] }}' })" class="text-blue-600 hover:text-blue-900">
                            <x-icon name="file-text" width="18" height="18" />
                        </button>
                        <button @click="$dispatch('edit-donasi', { id: '{{ $row['id'] }}' })" class="text-indigo-600 hover:text-indigo-900">
                            <x-icon name="pencil" width="18" height="18" />
                        </button>
                        @if(!($row['is_webhook'] ?? false))
                        <button @click="$dispatch('delete-donasi', { id: '{{ $row['id'] }}' })" class="text-red-500 hover:text-red-700">
                            <x-icon name="trash" width="18" height="18" />
                        </button>
                        @else
                        <span class="text-gray-400 cursor-not-allowed" title="Cannot delete webhook donations">
                            <x-icon name="trash" width="18" height="18" />
                        </span>
                        @endif
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="8" class="px-6 py-4 text-center text-gray-500">No donations found.</td>
            </tr>
            @endforelse
        </tbody>
    </table>

</div>
